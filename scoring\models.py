from django.db import models
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from resumes.models import Resume, JobDescription


class ScoringCriteria(models.Model):
    """Model for defining scoring criteria for job descriptions"""
    job_description = models.ForeignKey(JobDescription, on_delete=models.CASCADE, related_name='scoring_criteria')

    # Scoring weights (should sum to 100)
    skills_weight = models.PositiveIntegerField(
        default=40,
        validators=[MinV<PERSON>ueValidator(0), MaxValueValidator(100)],
        help_text="Weight for skills matching (0-100)"
    )
    experience_weight = models.PositiveIntegerField(
        default=30,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Weight for experience level (0-100)"
    )
    education_weight = models.PositiveIntegerField(
        default=20,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Weight for education background (0-100)"
    )
    other_weight = models.PositiveIntegerField(
        default=10,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Weight for other factors (0-100)"
    )

    # Minimum thresholds
    minimum_score = models.FloatField(
        default=60.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Minimum score to be considered for shortlisting"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['job_description']

    def __str__(self):
        return f"Scoring criteria for {self.job_description.title}"

    def clean(self):
        """Validate that weights sum to 100"""
        total_weight = self.skills_weight + self.experience_weight + self.education_weight + self.other_weight
        if total_weight != 100:
            from django.core.exceptions import ValidationError
            raise ValidationError(f"Weights must sum to 100, currently sum to {total_weight}")


class ResumeScore(models.Model):
    """Model for storing resume scores against job descriptions"""
    resume = models.ForeignKey(Resume, on_delete=models.CASCADE, related_name='scores')
    job_description = models.ForeignKey(JobDescription, on_delete=models.CASCADE, related_name='resume_scores')

    # Individual scores (0-100)
    skills_score = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Score for skills matching (0-100)"
    )
    experience_score = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Score for experience level (0-100)"
    )
    education_score = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Score for education background (0-100)"
    )
    other_score = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Score for other factors (0-100)"
    )

    # Overall score (weighted average)
    overall_score = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="Overall weighted score (0-100)"
    )

    # Detailed analysis
    skills_analysis = models.TextField(help_text="Detailed analysis of skills matching")
    experience_analysis = models.TextField(help_text="Detailed analysis of experience")
    education_analysis = models.TextField(help_text="Detailed analysis of education")
    overall_analysis = models.TextField(help_text="Overall analysis and recommendations")

    # Shortlisting decision
    is_shortlisted = models.BooleanField(default=False)
    shortlist_reason = models.TextField(blank=True, help_text="Reason for shortlisting decision")

    # Metadata
    scored_by_model = models.CharField(max_length=50, default='llama2')
    scored_at = models.DateTimeField(auto_now_add=True)
    scoring_time = models.FloatField(help_text="Time taken for scoring in seconds")

    # Raw LLM response for scoring
    raw_scoring_response = models.TextField(help_text="Raw response from LLM for scoring")

    class Meta:
        unique_together = ['resume', 'job_description']
        ordering = ['-overall_score', '-scored_at']

    def __str__(self):
        return f"{self.resume.original_filename} - {self.job_description.title}: {self.overall_score:.1f}%"

    def save(self, *args, **kwargs):
        """Calculate overall score based on weights"""
        try:
            criteria = self.job_description.scoring_criteria.get()
            self.overall_score = (
                (self.skills_score * criteria.skills_weight / 100) +
                (self.experience_score * criteria.experience_weight / 100) +
                (self.education_score * criteria.education_weight / 100) +
                (self.other_score * criteria.other_weight / 100)
            )

            # Determine shortlisting
            self.is_shortlisted = self.overall_score >= criteria.minimum_score

        except ScoringCriteria.DoesNotExist:
            # Default equal weights if no criteria defined
            self.overall_score = (self.skills_score + self.experience_score + self.education_score + self.other_score) / 4
            self.is_shortlisted = self.overall_score >= 60.0

        super().save(*args, **kwargs)


class BatchScoringJob(models.Model):
    """Model for tracking batch scoring jobs"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]

    job_description = models.ForeignKey(JobDescription, on_delete=models.CASCADE)
    resumes = models.ManyToManyField(Resume, related_name='batch_jobs')

    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    total_resumes = models.PositiveIntegerField(default=0)
    processed_resumes = models.PositiveIntegerField(default=0)
    failed_resumes = models.PositiveIntegerField(default=0)

    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True)

    created_by = models.ForeignKey('auth.User', on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Batch job for {self.job_description.title} - {self.status}"

    @property
    def progress_percentage(self):
        if self.total_resumes == 0:
            return 0
        return (self.processed_resumes / self.total_resumes) * 100
