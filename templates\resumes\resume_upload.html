{% extends 'base.html' %}

{% block title %}Upload Resume - Resume Parser{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-upload me-2"></i>Upload Resume
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Supported formats:</strong> PDF, DOC, DOCX, TXT files up to 5MB
                    </div>
                    
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="mb-4">
                            <label for="{{ form.file.id_for_label }}" class="form-label">Select Resume File *</label>
                            {{ form.file }}
                            {% if form.file.errors %}
                                <div class="text-danger mt-2">{{ form.file.errors }}</div>
                            {% endif %}
                            <div class="form-text">
                                Choose a resume file to upload. The system will automatically extract text and analyze the content using AI.
                            </div>
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-robot me-2"></i>
                            <strong>AI Processing:</strong> After upload, the resume will be processed using Ollama LLM to extract candidate information, skills, experience, and education details.
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'resume_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Back to Resumes
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload me-1"></i>Upload Resume
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <a href="{% url 'bulk_resume_upload' %}" class="btn btn-info w-100">
                                <i class="fas fa-cloud-upload-alt me-2"></i>
                                Bulk Upload Multiple Resumes
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="{% url 'job_list' %}" class="btn btn-success w-100">
                                <i class="fas fa-briefcase me-2"></i>
                                View Job Descriptions
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.querySelector('input[type="file"]');
    const form = document.querySelector('form');
    
    if (fileInput && form) {
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Check file size (5MB limit)
                if (file.size > 5 * 1024 * 1024) {
                    alert('File size must be less than 5MB');
                    e.target.value = '';
                    return;
                }
                
                // Check file type
                const allowedTypes = ['.pdf', '.doc', '.docx', '.txt'];
                const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
                if (!allowedTypes.includes(fileExtension)) {
                    alert('Please select a PDF, DOC, DOCX, or TXT file');
                    e.target.value = '';
                    return;
                }
            }
        });
        
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Uploading...';
            }
        });
    }
});
</script>
{% endblock %}
{% endblock %}
