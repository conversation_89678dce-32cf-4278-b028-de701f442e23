# Generated by Django 5.2.4 on 2025-07-08 11:28

import django.core.validators
import django.db.models.deletion
import resumes.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='JobDescription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('company', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('requirements', models.TextField()),
                ('skills_required', models.TextField(help_text='Comma-separated list of required skills')),
                ('experience_level', models.CharField(choices=[('entry', 'Entry Level'), ('mid', 'Mid Level'), ('senior', 'Senior Level'), ('executive', 'Executive Level')], default='mid', max_length=20)),
                ('location', models.CharField(blank=True, max_length=200)),
                ('salary_range', models.CharField(blank=True, max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Resume',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to=resumes.models.resume_upload_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf', 'doc', 'docx', 'txt'])])),
                ('original_filename', models.CharField(max_length=255)),
                ('file_size', models.PositiveIntegerField()),
                ('extracted_text', models.TextField(blank=True)),
                ('candidate_name', models.CharField(blank=True, max_length=200)),
                ('candidate_email', models.EmailField(blank=True, max_length=254)),
                ('candidate_phone', models.CharField(blank=True, max_length=20)),
                ('status', models.CharField(choices=[('uploaded', 'Uploaded'), ('processing', 'Processing'), ('processed', 'Processed'), ('error', 'Error')], default='uploaded', max_length=20)),
                ('processing_error', models.TextField(blank=True)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='ResumeAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('skills', models.TextField(help_text='JSON array of extracted skills')),
                ('experience_years', models.PositiveIntegerField(blank=True, null=True)),
                ('education', models.TextField(help_text='JSON array of education details')),
                ('work_experience', models.TextField(help_text='JSON array of work experience')),
                ('certifications', models.TextField(blank=True, help_text='JSON array of certifications')),
                ('analysis_model', models.CharField(default='llama2', max_length=50)),
                ('analysis_timestamp', models.DateTimeField(auto_now_add=True)),
                ('processing_time', models.FloatField(help_text='Time taken for analysis in seconds')),
                ('raw_llm_response', models.TextField(help_text='Raw response from LLM')),
                ('resume', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='analysis', to='resumes.resume')),
            ],
        ),
    ]
