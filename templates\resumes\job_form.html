{% extends 'base.html' %}

{% block title %}{{ title }} - Resume Parser{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-briefcase me-2"></i>{{ title }}
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.title.id_for_label }}" class="form-label">Job Title *</label>
                                {{ form.title }}
                                {% if form.title.errors %}
                                    <div class="text-danger">{{ form.title.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.company.id_for_label }}" class="form-label">Company *</label>
                                {{ form.company }}
                                {% if form.company.errors %}
                                    <div class="text-danger">{{ form.company.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.experience_level.id_for_label }}" class="form-label">Experience Level</label>
                                {{ form.experience_level }}
                                {% if form.experience_level.errors %}
                                    <div class="text-danger">{{ form.experience_level.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.location.id_for_label }}" class="form-label">Location</label>
                                {{ form.location }}
                                {% if form.location.errors %}
                                    <div class="text-danger">{{ form.location.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.salary_range.id_for_label }}" class="form-label">Salary Range</label>
                            {{ form.salary_range }}
                            {% if form.salary_range.errors %}
                                <div class="text-danger">{{ form.salary_range.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">Job Description *</label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger">{{ form.description.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.requirements.id_for_label }}" class="form-label">Requirements *</label>
                            {{ form.requirements }}
                            {% if form.requirements.errors %}
                                <div class="text-danger">{{ form.requirements.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.skills_required.id_for_label }}" class="form-label">Skills Required *</label>
                            {{ form.skills_required }}
                            <div class="form-text">Enter skills separated by commas (e.g., Python, Django, PostgreSQL)</div>
                            {% if form.skills_required.errors %}
                                <div class="text-danger">{{ form.skills_required.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'job_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Back to Jobs
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Save Job Description
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
