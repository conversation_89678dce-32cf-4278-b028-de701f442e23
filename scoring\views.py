import json
import logging
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.core.paginator import Paginator
from django.db.models import Q, Avg
from django.utils import timezone

from .models import ScoringCriteria, ResumeScore, BatchScoringJob
from resumes.models import JobDescription, Resume, ResumeAnalysis
from services.ollama_service import ollama_service

logger = logging.getLogger(__name__)


@login_required
def scoring_criteria_list(request):
    """List all scoring criteria"""
    criteria = ScoringCriteria.objects.select_related('job_description').order_by('-created_at')

    paginator = Paginator(criteria, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'scoring/criteria_list.html', {'page_obj': page_obj})


@login_required
def scoring_criteria_create(request, job_id):
    """Create scoring criteria for a job"""
    job = get_object_or_404(JobDescription, pk=job_id)

    # Check if criteria already exists
    try:
        criteria = job.scoring_criteria.get()
        messages.info(request, 'Scoring criteria already exists for this job. Redirecting to edit.')
        return redirect('scoring_criteria_edit', pk=criteria.pk)
    except ScoringCriteria.DoesNotExist:
        pass

    if request.method == 'POST':
        # Get form data
        skills_weight = int(request.POST.get('skills_weight', 40))
        experience_weight = int(request.POST.get('experience_weight', 30))
        education_weight = int(request.POST.get('education_weight', 20))
        other_weight = int(request.POST.get('other_weight', 10))
        minimum_score = float(request.POST.get('minimum_score', 60.0))

        # Validate weights sum to 100
        total_weight = skills_weight + experience_weight + education_weight + other_weight
        if total_weight != 100:
            messages.error(request, f'Weights must sum to 100. Current total: {total_weight}')
        else:
            criteria = ScoringCriteria.objects.create(
                job_description=job,
                skills_weight=skills_weight,
                experience_weight=experience_weight,
                education_weight=education_weight,
                other_weight=other_weight,
                minimum_score=minimum_score
            )
            messages.success(request, 'Scoring criteria created successfully!')
            return redirect('job_detail', pk=job.pk)

    return render(request, 'scoring/criteria_form.html', {
        'job': job,
        'title': f'Create Scoring Criteria for {job.title}'
    })


@login_required
def scoring_criteria_edit(request, pk):
    """Edit scoring criteria"""
    criteria = get_object_or_404(ScoringCriteria, pk=pk)

    if request.method == 'POST':
        # Get form data
        skills_weight = int(request.POST.get('skills_weight', criteria.skills_weight))
        experience_weight = int(request.POST.get('experience_weight', criteria.experience_weight))
        education_weight = int(request.POST.get('education_weight', criteria.education_weight))
        other_weight = int(request.POST.get('other_weight', criteria.other_weight))
        minimum_score = float(request.POST.get('minimum_score', criteria.minimum_score))

        # Validate weights sum to 100
        total_weight = skills_weight + experience_weight + education_weight + other_weight
        if total_weight != 100:
            messages.error(request, f'Weights must sum to 100. Current total: {total_weight}')
        else:
            criteria.skills_weight = skills_weight
            criteria.experience_weight = experience_weight
            criteria.education_weight = education_weight
            criteria.other_weight = other_weight
            criteria.minimum_score = minimum_score
            criteria.save()

            messages.success(request, 'Scoring criteria updated successfully!')
            return redirect('job_detail', pk=criteria.job_description.pk)

    return render(request, 'scoring/criteria_form.html', {
        'criteria': criteria,
        'job': criteria.job_description,
        'title': f'Edit Scoring Criteria for {criteria.job_description.title}'
    })


@login_required
@require_http_methods(["POST"])
def score_resume(request, resume_id, job_id):
    """Score a single resume against a job description"""
    resume = get_object_or_404(Resume, pk=resume_id)
    job = get_object_or_404(JobDescription, pk=job_id)

    # Check if resume is processed
    if resume.status != 'processed':
        return JsonResponse({'error': 'Resume must be processed before scoring'}, status=400)

    # Check if already scored
    existing_score = ResumeScore.objects.filter(resume=resume, job_description=job).first()
    if existing_score:
        return JsonResponse({'error': 'Resume already scored for this job'}, status=400)

    try:
        score_result = score_resume_against_job(resume, job)
        return JsonResponse({
            'success': True,
            'score': score_result.overall_score,
            'shortlisted': score_result.is_shortlisted
        })
    except Exception as e:
        logger.error(f"Error scoring resume {resume_id} against job {job_id}: {e}")
        return JsonResponse({'error': str(e)}, status=500)


def score_resume_against_job(resume, job_description):
    """Score a resume against a job description using LLM"""
    try:
        # Get resume analysis
        analysis = resume.analysis
        if not analysis:
            raise Exception("Resume analysis not found")

        # Prepare analysis data for LLM
        analysis_data = {
            'candidate_name': resume.candidate_name,
            'candidate_email': resume.candidate_email,
            'skills': json.loads(analysis.skills) if analysis.skills else [],
            'experience_years': analysis.experience_years,
            'education': json.loads(analysis.education) if analysis.education else [],
            'work_experience': json.loads(analysis.work_experience) if analysis.work_experience else [],
            'certifications': json.loads(analysis.certifications) if analysis.certifications else []
        }

        # Prepare job description data
        job_data = {
            'title': job_description.title,
            'company': job_description.company,
            'description': job_description.description,
            'requirements': job_description.requirements,
            'skills_required': job_description.skills_required,
            'experience_level': job_description.experience_level
        }

        # Get scoring from LLM
        scoring_response = ollama_service.score_resume(analysis_data, job_data)

        if not scoring_response['success']:
            raise Exception(f"LLM scoring failed: {scoring_response.get('error', 'Unknown error')}")

        # Parse scoring response
        try:
            scoring_data = json.loads(scoring_response['response'])
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing scoring response: {e}")
            logger.error(f"Raw response: {scoring_response['response']}")
            raise Exception(f"Error parsing LLM scoring response: {str(e)}")

        # Create ResumeScore object
        resume_score = ResumeScore.objects.create(
            resume=resume,
            job_description=job_description,
            skills_score=scoring_data.get('skills_score', 0),
            experience_score=scoring_data.get('experience_score', 0),
            education_score=scoring_data.get('education_score', 0),
            other_score=scoring_data.get('other_score', 0),
            skills_analysis=scoring_data.get('skills_analysis', ''),
            experience_analysis=scoring_data.get('experience_analysis', ''),
            education_analysis=scoring_data.get('education_analysis', ''),
            overall_analysis=scoring_data.get('overall_analysis', ''),
            shortlist_reason=scoring_data.get('shortlist_recommendation', ''),
            scored_by_model=scoring_response['model'],
            scoring_time=scoring_response['processing_time'],
            raw_scoring_response=scoring_response['response']
        )

        logger.info(f"Successfully scored resume {resume.id} against job {job_description.id}: {resume_score.overall_score}")
        return resume_score

    except Exception as e:
        logger.error(f"Error scoring resume {resume.id} against job {job_description.id}: {e}")
        raise


@login_required
def batch_score_resumes(request):
    """Batch score resumes against a job description"""
    if request.method == 'POST':
        job_id = request.POST.get('job_id')
        resume_ids = request.POST.getlist('resume_ids')

        if not job_id or not resume_ids:
            messages.error(request, 'Please select a job and at least one resume')
            return redirect('batch_score_resumes')

        job = get_object_or_404(JobDescription, pk=job_id)
        resumes = Resume.objects.filter(id__in=resume_ids, status='processed')

        if not resumes.exists():
            messages.error(request, 'No processed resumes found')
            return redirect('batch_score_resumes')

        # Create batch job
        batch_job = BatchScoringJob.objects.create(
            job_description=job,
            created_by=request.user,
            total_resumes=resumes.count(),
            status='pending'
        )
        batch_job.resumes.set(resumes)

        # Process batch (in production, this would be async)
        process_batch_scoring(batch_job.id)

        messages.success(request, f'Batch scoring started for {resumes.count()} resumes')
        return redirect('batch_job_detail', pk=batch_job.pk)

    # GET request - show form
    jobs = JobDescription.objects.filter(is_active=True).order_by('-created_at')
    resumes = Resume.objects.filter(status='processed').order_by('-uploaded_at')

    return render(request, 'scoring/batch_score.html', {
        'jobs': jobs,
        'resumes': resumes
    })


def process_batch_scoring(batch_job_id):
    """Process batch scoring job"""
    try:
        batch_job = BatchScoringJob.objects.get(id=batch_job_id)
        batch_job.status = 'running'
        batch_job.started_at = timezone.now()
        batch_job.save()

        processed_count = 0
        failed_count = 0

        for resume in batch_job.resumes.all():
            try:
                # Check if already scored
                if not ResumeScore.objects.filter(
                    resume=resume,
                    job_description=batch_job.job_description
                ).exists():
                    score_resume_against_job(resume, batch_job.job_description)

                processed_count += 1

            except Exception as e:
                logger.error(f"Error scoring resume {resume.id} in batch {batch_job_id}: {e}")
                failed_count += 1

            # Update progress
            batch_job.processed_resumes = processed_count
            batch_job.failed_resumes = failed_count
            batch_job.save()

        batch_job.status = 'completed'
        batch_job.completed_at = timezone.now()
        batch_job.save()

        logger.info(f"Batch job {batch_job_id} completed: {processed_count} processed, {failed_count} failed")

    except BatchScoringJob.DoesNotExist:
        logger.error(f"Batch job {batch_job_id} not found")
    except Exception as e:
        logger.error(f"Error processing batch job {batch_job_id}: {e}")
        try:
            batch_job = BatchScoringJob.objects.get(id=batch_job_id)
            batch_job.status = 'failed'
            batch_job.error_message = str(e)
            batch_job.save()
        except:
            pass


@login_required
def resume_scores_list(request):
    """List all resume scores"""
    scores = ResumeScore.objects.select_related('resume', 'job_description').order_by('-overall_score')

    # Filter by job if specified
    job_id = request.GET.get('job_id')
    if job_id:
        scores = scores.filter(job_description_id=job_id)

    # Filter by shortlisted status
    shortlisted = request.GET.get('shortlisted')
    if shortlisted == 'true':
        scores = scores.filter(is_shortlisted=True)
    elif shortlisted == 'false':
        scores = scores.filter(is_shortlisted=False)

    paginator = Paginator(scores, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get jobs for filter dropdown
    jobs = JobDescription.objects.filter(is_active=True).order_by('title')

    return render(request, 'scoring/scores_list.html', {
        'page_obj': page_obj,
        'jobs': jobs,
        'current_job_id': job_id,
        'current_shortlisted': shortlisted
    })


@login_required
def resume_score_detail(request, pk):
    """Resume score detail view"""
    score = get_object_or_404(ResumeScore, pk=pk)
    return render(request, 'scoring/score_detail.html', {'score': score})


@login_required
def batch_jobs_list(request):
    """List batch scoring jobs"""
    jobs = BatchScoringJob.objects.select_related('job_description', 'created_by').order_by('-created_at')

    paginator = Paginator(jobs, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'scoring/batch_jobs_list.html', {'page_obj': page_obj})


@login_required
def batch_job_detail(request, pk):
    """Batch job detail view"""
    batch_job = get_object_or_404(BatchScoringJob, pk=pk)

    # Get scores created by this batch job
    scores = ResumeScore.objects.filter(
        job_description=batch_job.job_description,
        resume__in=batch_job.resumes.all()
    ).select_related('resume').order_by('-overall_score')

    return render(request, 'scoring/batch_job_detail.html', {
        'batch_job': batch_job,
        'scores': scores
    })
