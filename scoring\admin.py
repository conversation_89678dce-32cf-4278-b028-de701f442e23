from django.contrib import admin
from .models import ScoringCriteria, ResumeScore, BatchScoringJob


@admin.register(ScoringCriteria)
class ScoringCriteriaAdmin(admin.ModelAdmin):
    list_display = ['job_description', 'skills_weight', 'experience_weight', 'education_weight', 'other_weight', 'minimum_score']
    list_filter = ['minimum_score', 'created_at']
    search_fields = ['job_description__title', 'job_description__company']
    readonly_fields = ['created_at', 'updated_at']
    fieldsets = (
        ('Job Description', {
            'fields': ('job_description',)
        }),
        ('Scoring Weights', {
            'fields': ('skills_weight', 'experience_weight', 'education_weight', 'other_weight'),
            'description': 'Weights must sum to 100'
        }),
        ('Thresholds', {
            'fields': ('minimum_score',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = list(self.readonly_fields)
        if obj:  # If editing existing object
            readonly_fields.append('job_description')
        return readonly_fields


@admin.register(ResumeScore)
class ResumeScoreAdmin(admin.ModelAdmin):
    list_display = ['resume', 'job_description', 'overall_score', 'is_shortlisted', 'scored_by_model', 'scored_at']
    list_filter = ['is_shortlisted', 'scored_by_model', 'scored_at', 'overall_score']
    search_fields = ['resume__original_filename', 'resume__candidate_name', 'job_description__title']
    readonly_fields = ['overall_score', 'is_shortlisted', 'scored_at', 'scoring_time', 'raw_scoring_response']
    fieldsets = (
        ('Basic Information', {
            'fields': ('resume', 'job_description')
        }),
        ('Individual Scores', {
            'fields': ('skills_score', 'experience_score', 'education_score', 'other_score')
        }),
        ('Overall Results', {
            'fields': ('overall_score', 'is_shortlisted', 'shortlist_reason')
        }),
        ('Detailed Analysis', {
            'fields': ('skills_analysis', 'experience_analysis', 'education_analysis', 'overall_analysis'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('scored_by_model', 'scored_at', 'scoring_time'),
            'classes': ('collapse',)
        }),
        ('Raw Data', {
            'fields': ('raw_scoring_response',),
            'classes': ('collapse',)
        }),
    )

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = list(self.readonly_fields)
        if obj:  # If editing existing object
            readonly_fields.extend(['resume', 'job_description'])
        return readonly_fields


@admin.register(BatchScoringJob)
class BatchScoringJobAdmin(admin.ModelAdmin):
    list_display = ['job_description', 'status', 'total_resumes', 'processed_resumes', 'failed_resumes', 'progress_percentage', 'created_at']
    list_filter = ['status', 'created_at', 'started_at', 'completed_at']
    search_fields = ['job_description__title', 'job_description__company']
    readonly_fields = ['progress_percentage', 'started_at', 'completed_at', 'created_at']
    filter_horizontal = ['resumes']
    fieldsets = (
        ('Job Information', {
            'fields': ('job_description', 'created_by')
        }),
        ('Resumes', {
            'fields': ('resumes',)
        }),
        ('Status', {
            'fields': ('status', 'total_resumes', 'processed_resumes', 'failed_resumes', 'progress_percentage')
        }),
        ('Timing', {
            'fields': ('started_at', 'completed_at', 'created_at'),
            'classes': ('collapse',)
        }),
        ('Error Information', {
            'fields': ('error_message',),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # If creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = list(self.readonly_fields)
        if obj and obj.status in ['running', 'completed']:
            readonly_fields.extend(['job_description', 'resumes'])
        return readonly_fields
