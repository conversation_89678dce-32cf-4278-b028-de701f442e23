from django.db import models
from django.contrib.auth.models import User
from django.core.validators import FileExtensionValidator
import os


def resume_upload_path(instance, filename):
    """Generate upload path for resume files"""
    return f'resumes/{instance.id}/{filename}'


class JobDescription(models.Model):
    """Model for storing job descriptions"""
    title = models.CharField(max_length=200)
    company = models.CharField(max_length=200)
    description = models.TextField()
    requirements = models.TextField()
    skills_required = models.TextField(help_text="Comma-separated list of required skills")
    experience_level = models.CharField(
        max_length=20,
        choices=[
            ('entry', 'Entry Level'),
            ('mid', 'Mid Level'),
            ('senior', 'Senior Level'),
            ('executive', 'Executive Level'),
        ],
        default='mid'
    )
    location = models.CharField(max_length=200, blank=True)
    salary_range = models.CharField(max_length=100, blank=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} at {self.company}"


class Resume(models.Model):
    """Model for storing uploaded resumes"""
    STATUS_CHOICES = [
        ('uploaded', 'Uploaded'),
        ('processing', 'Processing'),
        ('processed', 'Processed'),
        ('error', 'Error'),
    ]

    # File information
    file = models.FileField(
        upload_to=resume_upload_path,
        validators=[FileExtensionValidator(allowed_extensions=['pdf', 'doc', 'docx', 'txt'])]
    )
    original_filename = models.CharField(max_length=255)
    file_size = models.PositiveIntegerField()  # in bytes

    # Extracted content
    extracted_text = models.TextField(blank=True)

    # Parsed information
    candidate_name = models.CharField(max_length=200, blank=True)
    candidate_email = models.EmailField(blank=True)
    candidate_phone = models.CharField(max_length=20, blank=True)

    # Processing status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='uploaded')
    processing_error = models.TextField(blank=True)

    # Metadata
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"{self.original_filename} - {self.candidate_name or 'Unknown'}"

    def get_file_extension(self):
        return os.path.splitext(self.original_filename)[1].lower()


class ResumeAnalysis(models.Model):
    """Model for storing LLM analysis of resumes"""
    resume = models.OneToOneField(Resume, on_delete=models.CASCADE, related_name='analysis')

    # Extracted structured data
    skills = models.TextField(help_text="JSON array of extracted skills")
    experience_years = models.PositiveIntegerField(null=True, blank=True)
    education = models.TextField(help_text="JSON array of education details")
    work_experience = models.TextField(help_text="JSON array of work experience")
    certifications = models.TextField(blank=True, help_text="JSON array of certifications")

    # Analysis metadata
    analysis_model = models.CharField(max_length=50, default='llama2')
    analysis_timestamp = models.DateTimeField(auto_now_add=True)
    processing_time = models.FloatField(help_text="Time taken for analysis in seconds")

    # Raw LLM response
    raw_llm_response = models.TextField(help_text="Raw response from LLM")

    def __str__(self):
        return f"Analysis for {self.resume.original_filename}"
