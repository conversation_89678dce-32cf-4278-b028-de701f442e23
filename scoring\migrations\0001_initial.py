# Generated by Django 5.2.4 on 2025-07-08 11:28

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('resumes', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BatchScoringJob',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('total_resumes', models.PositiveIntegerField(default=0)),
                ('processed_resumes', models.PositiveIntegerField(default=0)),
                ('failed_resumes', models.PositiveIntegerField(default=0)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('job_description', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='resumes.jobdescription')),
                ('resumes', models.ManyToManyField(related_name='batch_jobs', to='resumes.resume')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ResumeScore',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('skills_score', models.FloatField(help_text='Score for skills matching (0-100)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('experience_score', models.FloatField(help_text='Score for experience level (0-100)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('education_score', models.FloatField(help_text='Score for education background (0-100)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('other_score', models.FloatField(help_text='Score for other factors (0-100)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('overall_score', models.FloatField(help_text='Overall weighted score (0-100)', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('skills_analysis', models.TextField(help_text='Detailed analysis of skills matching')),
                ('experience_analysis', models.TextField(help_text='Detailed analysis of experience')),
                ('education_analysis', models.TextField(help_text='Detailed analysis of education')),
                ('overall_analysis', models.TextField(help_text='Overall analysis and recommendations')),
                ('is_shortlisted', models.BooleanField(default=False)),
                ('shortlist_reason', models.TextField(blank=True, help_text='Reason for shortlisting decision')),
                ('scored_by_model', models.CharField(default='llama2', max_length=50)),
                ('scored_at', models.DateTimeField(auto_now_add=True)),
                ('scoring_time', models.FloatField(help_text='Time taken for scoring in seconds')),
                ('raw_scoring_response', models.TextField(help_text='Raw response from LLM for scoring')),
                ('job_description', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resume_scores', to='resumes.jobdescription')),
                ('resume', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='scores', to='resumes.resume')),
            ],
            options={
                'ordering': ['-overall_score', '-scored_at'],
                'unique_together': {('resume', 'job_description')},
            },
        ),
        migrations.CreateModel(
            name='ScoringCriteria',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('skills_weight', models.PositiveIntegerField(default=40, help_text='Weight for skills matching (0-100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('experience_weight', models.PositiveIntegerField(default=30, help_text='Weight for experience level (0-100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('education_weight', models.PositiveIntegerField(default=20, help_text='Weight for education background (0-100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('other_weight', models.PositiveIntegerField(default=10, help_text='Weight for other factors (0-100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('minimum_score', models.FloatField(default=60.0, help_text='Minimum score to be considered for shortlisting', validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('job_description', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='scoring_criteria', to='resumes.jobdescription')),
            ],
            options={
                'unique_together': {('job_description',)},
            },
        ),
    ]
