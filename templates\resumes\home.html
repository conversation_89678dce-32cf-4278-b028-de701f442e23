{% extends 'base.html' %}

{% block title %}Dashboard - Resume Parser{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-4">
                <i class="fas fa-tachometer-alt me-3"></i>Dashboard
            </h1>
            <p class="lead">Bulk Resume Parser and Scoring System with Ollama LLM</p>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_jobs }}</h4>
                            <p class="mb-0">Active Jobs</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-briefcase fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'job_list' %}" class="text-white text-decoration-none">
                        View all jobs <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_resumes }}</h4>
                            <p class="mb-0">Total Resumes</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-file-pdf fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'resume_list' %}" class="text-white text-decoration-none">
                        View all resumes <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ processed_resumes }}</h4>
                            <p class="mb-0">Processed</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'resume_list' %}?status=processed" class="text-white text-decoration-none">
                        View processed <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_resumes|add:"-"|add:processed_resumes }}</h4>
                            <p class="mb-0">Pending</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'resume_list' %}?status=uploaded" class="text-dark text-decoration-none">
                        View pending <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'job_create' %}" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-plus me-2"></i>Create Job
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'resume_upload' %}" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-upload me-2"></i>Upload Resume
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'bulk_resume_upload' %}" class="btn btn-info btn-lg w-100">
                                <i class="fas fa-cloud-upload-alt me-2"></i>Bulk Upload
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'batch_score_resumes' %}" class="btn btn-warning btn-lg w-100">
                                <i class="fas fa-chart-bar me-2"></i>Batch Score
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-briefcase me-2"></i>Recent Jobs
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_jobs %}
                        <div class="list-group list-group-flush">
                            {% for job in recent_jobs %}
                                <div class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold">
                                            <a href="{% url 'job_detail' job.pk %}" class="text-decoration-none">
                                                {{ job.title }}
                                            </a>
                                        </div>
                                        <small class="text-muted">{{ job.company }}</small>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">{{ job.experience_level }}</span>
                                </div>
                            {% endfor %}
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'job_list' %}" class="btn btn-outline-primary btn-sm">
                                View All Jobs <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    {% else %}
                        <p class="text-muted">No jobs created yet.</p>
                        <a href="{% url 'job_create' %}" class="btn btn-primary">Create First Job</a>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-pdf me-2"></i>Recent Resumes
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_resumes %}
                        <div class="list-group list-group-flush">
                            {% for resume in recent_resumes %}
                                <div class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold">
                                            <a href="{% url 'resume_detail' resume.pk %}" class="text-decoration-none">
                                                {{ resume.candidate_name|default:resume.original_filename }}
                                            </a>
                                        </div>
                                        <small class="text-muted">{{ resume.uploaded_at|date:"M d, Y H:i" }}</small>
                                    </div>
                                    <span class="processing-status status-{{ resume.status }}"></span>
                                    <span class="badge bg-secondary">{{ resume.status }}</span>
                                </div>
                            {% endfor %}
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'resume_list' %}" class="btn btn-outline-success btn-sm">
                                View All Resumes <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    {% else %}
                        <p class="text-muted">No resumes uploaded yet.</p>
                        <a href="{% url 'resume_upload' %}" class="btn btn-success">Upload First Resume</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
