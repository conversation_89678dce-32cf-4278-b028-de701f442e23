"""
Forms for resume upload and job description management
"""
from django import forms
from django.core.exceptions import ValidationError
from .models import JobDescription, Resume
from services.file_processor import file_processor


class MultipleFileInput(forms.ClearableFileInput):
    """Custom widget for multiple file uploads"""
    allow_multiple_selected = True


class MultipleFileField(forms.FileField):
    """Custom field for multiple file uploads"""
    def __init__(self, *args, **kwargs):
        kwargs.setdefault("widget", MultipleFileInput())
        super().__init__(*args, **kwargs)

    def clean(self, data, initial=None):
        single_file_clean = super().clean
        if isinstance(data, (list, tuple)):
            result = [single_file_clean(d, initial) for d in data]
        else:
            result = single_file_clean(data, initial)
        return result


class JobDescriptionForm(forms.ModelForm):
    """Form for creating/editing job descriptions"""
    
    class Meta:
        model = JobDescription
        fields = [
            'title', 'company', 'description', 'requirements', 
            'skills_required', 'experience_level', 'location', 'salary_range'
        ]
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g., Senior Python Developer'
            }),
            'company': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g., Tech Corp Inc.'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 5,
                'placeholder': 'Detailed job description...'
            }),
            'requirements': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 5,
                'placeholder': 'Job requirements and qualifications...'
            }),
            'skills_required': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Python, Django, PostgreSQL, AWS, etc.'
            }),
            'experience_level': forms.Select(attrs={
                'class': 'form-control'
            }),
            'location': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g., New York, NY (Remote)'
            }),
            'salary_range': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g., $80,000 - $120,000'
            }),
        }

    def clean_skills_required(self):
        """Validate and clean skills_required field"""
        skills = self.cleaned_data.get('skills_required', '')
        if not skills.strip():
            raise ValidationError('Skills required field cannot be empty')
        return skills.strip()


class ResumeUploadForm(forms.ModelForm):
    """Form for uploading resumes"""
    
    class Meta:
        model = Resume
        fields = ['file']
        widgets = {
            'file': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx,.txt',
                'multiple': False
            })
        }

    def clean_file(self):
        """Validate uploaded file"""
        file = self.cleaned_data.get('file')
        if not file:
            raise ValidationError('Please select a file to upload')
        
        # Use our file processor to validate
        validation_result = file_processor.validate_file(file)
        if not validation_result['valid']:
            raise ValidationError(validation_result['error'])
        
        return file


class BulkResumeUploadForm(forms.Form):
    """Form for bulk resume upload"""

    files = MultipleFileField(
        widget=MultipleFileInput(attrs={
            'class': 'form-control',
            'accept': '.pdf,.doc,.docx,.txt',
            'multiple': True
        }),
        help_text='Select multiple resume files (PDF, DOC, DOCX, TXT)'
    )
    
    job_description = forms.ModelChoiceField(
        queryset=JobDescription.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control'}),
        help_text='Select job description to score resumes against'
    )
    
    auto_process = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        help_text='Automatically process and score resumes after upload'
    )

    def clean_files(self):
        """Validate uploaded files"""
        files = self.files.getlist('files')
        if not files:
            raise ValidationError('Please select at least one file to upload')
        
        # Validate each file
        for file in files:
            validation_result = file_processor.validate_file(file)
            if not validation_result['valid']:
                raise ValidationError(f'File {file.name}: {validation_result["error"]}')
        
        return files


class JobDescriptionSearchForm(forms.Form):
    """Form for searching job descriptions"""
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by title, company, or skills...'
        })
    )
    
    experience_level = forms.ChoiceField(
        required=False,
        choices=[('', 'All Levels')] + JobDescription._meta.get_field('experience_level').choices,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    company = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Filter by company...'
        })
    )


class ResumeSearchForm(forms.Form):
    """Form for searching resumes"""
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by name, email, or skills...'
        })
    )
    
    status = forms.ChoiceField(
        required=False,
        choices=[('', 'All Statuses')] + Resume.STATUS_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
