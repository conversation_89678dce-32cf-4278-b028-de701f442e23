from django.contrib import admin
from .models import JobDescription, Resume, ResumeAnalysis


@admin.register(JobDescription)
class JobDescriptionAdmin(admin.ModelAdmin):
    list_display = ['title', 'company', 'experience_level', 'location', 'created_by', 'created_at', 'is_active']
    list_filter = ['experience_level', 'is_active', 'created_at', 'company']
    search_fields = ['title', 'company', 'description', 'requirements']
    readonly_fields = ['created_at', 'updated_at']
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'company', 'experience_level', 'location', 'salary_range')
        }),
        ('Job Details', {
            'fields': ('description', 'requirements', 'skills_required')
        }),
        ('Metadata', {
            'fields': ('created_by', 'is_active', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # If creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Resume)
class ResumeAdmin(admin.ModelAdmin):
    list_display = ['original_filename', 'candidate_name', 'candidate_email', 'status', 'uploaded_by', 'uploaded_at']
    list_filter = ['status', 'uploaded_at', 'processed_at']
    search_fields = ['original_filename', 'candidate_name', 'candidate_email', 'extracted_text']
    readonly_fields = ['file_size', 'uploaded_at', 'processed_at', 'extracted_text']
    fieldsets = (
        ('File Information', {
            'fields': ('file', 'original_filename', 'file_size')
        }),
        ('Candidate Information', {
            'fields': ('candidate_name', 'candidate_email', 'candidate_phone')
        }),
        ('Processing Status', {
            'fields': ('status', 'processing_error', 'processed_at')
        }),
        ('Content', {
            'fields': ('extracted_text',),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('uploaded_by', 'uploaded_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # If creating new object
            obj.uploaded_by = request.user
            if obj.file:
                obj.original_filename = obj.file.name
                obj.file_size = obj.file.size
        super().save_model(request, obj, form, change)


@admin.register(ResumeAnalysis)
class ResumeAnalysisAdmin(admin.ModelAdmin):
    list_display = ['resume', 'experience_years', 'analysis_model', 'analysis_timestamp', 'processing_time']
    list_filter = ['analysis_model', 'analysis_timestamp']
    search_fields = ['resume__original_filename', 'resume__candidate_name', 'skills', 'education']
    readonly_fields = ['analysis_timestamp', 'processing_time', 'raw_llm_response']
    fieldsets = (
        ('Resume Information', {
            'fields': ('resume',)
        }),
        ('Extracted Data', {
            'fields': ('skills', 'experience_years', 'education', 'work_experience', 'certifications')
        }),
        ('Analysis Metadata', {
            'fields': ('analysis_model', 'analysis_timestamp', 'processing_time'),
            'classes': ('collapse',)
        }),
        ('Raw Data', {
            'fields': ('raw_llm_response',),
            'classes': ('collapse',)
        }),
    )
