"""
Django management command to setup Ollama for resume parsing
"""
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from services.ollama_service import ollama_service


class Command(BaseCommand):
    help = 'Setup Ollama for resume parsing - check status and download models'

    def add_arguments(self, parser):
        parser.add_argument(
            '--model',
            type=str,
            default='llama2',
            help='Model to download (default: llama2)'
        )
        parser.add_argument(
            '--force-download',
            action='store_true',
            help='Force download model even if it exists'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up Ollama for Resume Parser...'))
        
        # Check if Ollama is running
        self.stdout.write('Checking Ollama server status...')
        if not ollama_service.is_ollama_running():
            raise CommandError(
                'Ollama server is not running. Please start Ollama first.\n'
                'You can start it by running: ollama serve'
            )
        
        self.stdout.write(self.style.SUCCESS('✓ Ollama server is running'))
        
        # List available models
        self.stdout.write('Checking available models...')
        models = ollama_service.list_models()
        
        if models:
            self.stdout.write('Available models:')
            for model in models:
                self.stdout.write(f"  - {model.get('name', 'Unknown')}")
        else:
            self.stdout.write('No models found locally')
        
        # Check if requested model exists
        model_name = options['model']
        model_exists = any(model.get('name', '').startswith(model_name) for model in models)
        
        if model_exists and not options['force_download']:
            self.stdout.write(self.style.SUCCESS(f'✓ Model {model_name} is already available'))
        else:
            # Download the model
            self.stdout.write(f'Downloading model: {model_name}...')
            self.stdout.write(self.style.WARNING('This may take several minutes depending on your internet connection'))
            
            if ollama_service.pull_model(model_name):
                self.stdout.write(self.style.SUCCESS(f'✓ Successfully downloaded {model_name}'))
            else:
                raise CommandError(f'Failed to download model: {model_name}')
        
        # Test the model
        self.stdout.write('Testing model...')
        test_response = ollama_service.generate_response(
            "Hello! Please respond with 'Resume parser is ready!'",
            model=model_name
        )
        
        if test_response['success']:
            self.stdout.write(self.style.SUCCESS('✓ Model test successful'))
            self.stdout.write(f"Response: {test_response['response']}")
            self.stdout.write(f"Processing time: {test_response['processing_time']:.2f} seconds")
        else:
            raise CommandError(f'Model test failed: {test_response.get("error", "Unknown error")}')
        
        # Update settings if different model was used
        if model_name != settings.OLLAMA_MODEL:
            self.stdout.write(
                self.style.WARNING(
                    f'Note: You used model "{model_name}" but settings.OLLAMA_MODEL is set to "{settings.OLLAMA_MODEL}"\n'
                    f'Consider updating your .env file to use OLLAMA_MODEL={model_name}'
                )
            )
        
        self.stdout.write(self.style.SUCCESS('\n🎉 Ollama setup completed successfully!'))
        self.stdout.write('You can now use the resume parser with LLM analysis.')
