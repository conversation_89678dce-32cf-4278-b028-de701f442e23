"""
URL patterns for scoring app
"""
from django.urls import path
from . import views

urlpatterns = [
    # Scoring criteria
    path('criteria/', views.scoring_criteria_list, name='scoring_criteria_list'),
    path('criteria/create/<int:job_id>/', views.scoring_criteria_create, name='scoring_criteria_create'),
    path('criteria/<int:pk>/edit/', views.scoring_criteria_edit, name='scoring_criteria_edit'),
    
    # Resume scoring
    path('score/<int:resume_id>/<int:job_id>/', views.score_resume, name='score_resume'),
    path('batch-score/', views.batch_score_resumes, name='batch_score_resumes'),
    path('scores/', views.resume_scores_list, name='resume_scores_list'),
    path('scores/<int:pk>/', views.resume_score_detail, name='resume_score_detail'),
    
    # Batch jobs
    path('batch-jobs/', views.batch_jobs_list, name='batch_jobs_list'),
    path('batch-jobs/<int:pk>/', views.batch_job_detail, name='batch_job_detail'),
]
