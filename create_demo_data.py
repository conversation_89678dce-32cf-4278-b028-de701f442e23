#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create demo data for the resume parser system
"""
import os
import sys
import django
from django.conf import settings

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'resume_parser_project.settings')
django.setup()

from django.contrib.auth.models import User
from resumes.models import JobDescription
from scoring.models import ScoringCriteria


def create_demo_user():
    """Create a demo user"""
    user, created = User.objects.get_or_create(
        username='demo_user',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Demo',
            'last_name': 'User'
        }
    )
    if created:
        print("✅ Created demo user")
    else:
        print("✅ Demo user already exists")
    return user


def create_sample_jobs(user):
    """Create sample job descriptions"""
    jobs_data = [
        {
            'title': 'Senior Python Developer',
            'company': 'TechCorp Solutions',
            'description': '''We are looking for an experienced Python Developer to join our dynamic team. 
            You will be responsible for developing scalable web applications, designing APIs, and working 
            with cross-functional teams to deliver high-quality software solutions.''',
            'requirements': '''• 5+ years of Python development experience
            • Strong experience with Django or Flask frameworks
            • Proficiency in PostgreSQL or MySQL databases
            • Experience with REST API development
            • Knowledge of Git version control
            • Experience with cloud platforms (AWS, Azure, or GCP)
            • Strong problem-solving and communication skills''',
            'skills_required': 'Python, Django, PostgreSQL, REST APIs, Git, AWS, Docker',
            'experience_level': 'senior',
            'location': 'New York, NY (Remote)',
            'salary_range': '$120,000 - $150,000'
        },
        {
            'title': 'Full Stack Developer',
            'company': 'StartupXYZ',
            'description': '''Join our fast-growing startup as a Full Stack Developer! You'll work on 
            cutting-edge projects, build user-facing features, and help scale our platform to serve 
            millions of users.''',
            'requirements': '''• 3+ years of full-stack development experience
            • Proficiency in Python and JavaScript
            • Experience with React or Vue.js
            • Knowledge of Django or Flask
            • Database design and optimization skills
            • Experience with modern development tools and practices''',
            'skills_required': 'Python, JavaScript, React, Django, PostgreSQL, HTML, CSS',
            'experience_level': 'mid',
            'location': 'San Francisco, CA',
            'salary_range': '$90,000 - $120,000'
        },
        {
            'title': 'Data Engineer',
            'company': 'DataFlow Analytics',
            'description': '''We're seeking a talented Data Engineer to design and build robust data 
            pipelines, optimize data workflows, and ensure data quality across our analytics platform.''',
            'requirements': '''• 4+ years of data engineering experience
            • Strong Python programming skills
            • Experience with data pipeline tools (Airflow, Luigi, etc.)
            • Knowledge of big data technologies (Spark, Hadoop)
            • SQL expertise and database optimization
            • Cloud platform experience (AWS, GCP)''',
            'skills_required': 'Python, SQL, Apache Spark, Airflow, AWS, PostgreSQL, ETL',
            'experience_level': 'mid',
            'location': 'Austin, TX (Hybrid)',
            'salary_range': '$100,000 - $130,000'
        },
        {
            'title': 'Junior Python Developer',
            'company': 'Learning Labs Inc',
            'description': '''Perfect opportunity for a junior developer to grow their skills! You'll work 
            on educational technology projects, learn from senior developers, and contribute to meaningful 
            software that helps students learn.''',
            'requirements': '''• 1-2 years of Python experience or strong bootcamp/academic background
            • Basic understanding of web development concepts
            • Familiarity with Django or Flask
            • Knowledge of HTML, CSS, and JavaScript
            • Eagerness to learn and grow
            • Good communication and teamwork skills''',
            'skills_required': 'Python, Django, HTML, CSS, JavaScript, Git',
            'experience_level': 'entry',
            'location': 'Remote',
            'salary_range': '$60,000 - $80,000'
        }
    ]
    
    created_jobs = []
    for job_data in jobs_data:
        job, created = JobDescription.objects.get_or_create(
            title=job_data['title'],
            company=job_data['company'],
            defaults={
                **job_data,
                'created_by': user
            }
        )
        if created:
            print(f"✅ Created job: {job.title} at {job.company}")
            
            # Create default scoring criteria
            criteria, criteria_created = ScoringCriteria.objects.get_or_create(
                job_description=job,
                defaults={
                    'skills_weight': 40,
                    'experience_weight': 30,
                    'education_weight': 20,
                    'other_weight': 10,
                    'minimum_score': 60.0
                }
            )
            if criteria_created:
                print(f"  ✅ Created scoring criteria for {job.title}")
        else:
            print(f"✅ Job already exists: {job.title} at {job.company}")
        
        created_jobs.append(job)
    
    return created_jobs


def main():
    """Create all demo data"""
    print("🚀 Creating demo data for Resume Parser System\n")
    
    try:
        # Create demo user
        user = create_demo_user()
        
        # Create sample jobs
        jobs = create_sample_jobs(user)
        
        print(f"\n🎉 Demo data created successfully!")
        print(f"📊 Summary:")
        print(f"   - Demo user: {user.username}")
        print(f"   - Job descriptions: {len(jobs)}")
        print(f"   - Scoring criteria: {len(jobs)}")
        
        print(f"\n📝 Next steps:")
        print(f"1. Visit http://127.0.0.1:8000/ to see the dashboard")
        print(f"2. Go to Jobs to view the sample job descriptions")
        print(f"3. Upload the sample_resume.txt file to test the system")
        print(f"4. Try scoring resumes against different job descriptions")
        
    except Exception as e:
        print(f"❌ Error creating demo data: {e}")
        return False
    
    return True


if __name__ == '__main__':
    main()
