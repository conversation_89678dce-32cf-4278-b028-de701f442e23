"""
File processing service for resume parsing
"""
import os
import logging
from typing import Optional, Dict, Any
import PyPDF2
import docx
import magic
from django.core.files.uploadedfile import UploadedFile

logger = logging.getLogger(__name__)


class FileProcessor:
    """Service class for processing uploaded resume files"""
    
    SUPPORTED_EXTENSIONS = ['.pdf', '.doc', '.docx', '.txt']
    
    def __init__(self):
        self.magic = magic.Magic(mime=True)
    
    def validate_file(self, file: UploadedFile) -> Dict[str, Any]:
        """Validate uploaded file"""
        result = {
            'valid': False,
            'error': None,
            'file_info': {}
        }
        
        try:
            # Check file size
            if file.size > 5 * 1024 * 1024:  # 5MB limit
                result['error'] = 'File size exceeds 5MB limit'
                return result
            
            # Check file extension
            file_extension = os.path.splitext(file.name)[1].lower()
            if file_extension not in self.SUPPORTED_EXTENSIONS:
                result['error'] = f'Unsupported file type. Supported types: {", ".join(self.SUPPORTED_EXTENSIONS)}'
                return result
            
            # Check MIME type
            file.seek(0)  # Reset file pointer
            file_content = file.read(1024)  # Read first 1KB for MIME detection
            file.seek(0)  # Reset file pointer again
            
            mime_type = self.magic.from_buffer(file_content)
            
            expected_mimes = {
                '.pdf': 'application/pdf',
                '.doc': 'application/msword',
                '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                '.txt': 'text/plain'
            }
            
            if file_extension in expected_mimes:
                expected_mime = expected_mimes[file_extension]
                if not mime_type.startswith(expected_mime.split('/')[0]):
                    logger.warning(f"MIME type mismatch: expected {expected_mime}, got {mime_type}")
            
            result['valid'] = True
            result['file_info'] = {
                'extension': file_extension,
                'mime_type': mime_type,
                'size': file.size,
                'name': file.name
            }
            
        except Exception as e:
            logger.error(f"Error validating file: {e}")
            result['error'] = f'Error validating file: {str(e)}'
        
        return result
    
    def extract_text_from_pdf(self, file: UploadedFile) -> str:
        """Extract text from PDF file"""
        try:
            file.seek(0)
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
            
            return text.strip()
            
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {e}")
            raise Exception(f"Failed to extract text from PDF: {str(e)}")
    
    def extract_text_from_docx(self, file: UploadedFile) -> str:
        """Extract text from DOCX file"""
        try:
            file.seek(0)
            doc = docx.Document(file)
            text = ""
            
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            return text.strip()
            
        except Exception as e:
            logger.error(f"Error extracting text from DOCX: {e}")
            raise Exception(f"Failed to extract text from DOCX: {str(e)}")
    
    def extract_text_from_doc(self, file: UploadedFile) -> str:
        """Extract text from DOC file (legacy format)"""
        # For DOC files, we'll need a different approach
        # This is a simplified version - in production, you might want to use python-docx2txt or antiword
        try:
            file.seek(0)
            content = file.read()
            # This is a very basic extraction - DOC format is complex
            # In production, consider using antiword or converting to DOCX first
            text = content.decode('utf-8', errors='ignore')
            return text.strip()
            
        except Exception as e:
            logger.error(f"Error extracting text from DOC: {e}")
            raise Exception(f"Failed to extract text from DOC: {str(e)}")
    
    def extract_text_from_txt(self, file: UploadedFile) -> str:
        """Extract text from TXT file"""
        try:
            file.seek(0)
            content = file.read()
            
            # Try different encodings
            encodings = ['utf-8', 'latin-1', 'cp1252']
            for encoding in encodings:
                try:
                    text = content.decode(encoding)
                    return text.strip()
                except UnicodeDecodeError:
                    continue
            
            # If all encodings fail, use utf-8 with error handling
            text = content.decode('utf-8', errors='ignore')
            return text.strip()
            
        except Exception as e:
            logger.error(f"Error extracting text from TXT: {e}")
            raise Exception(f"Failed to extract text from TXT: {str(e)}")
    
    def extract_text(self, file: UploadedFile) -> str:
        """Extract text from uploaded file based on its type"""
        validation_result = self.validate_file(file)
        
        if not validation_result['valid']:
            raise Exception(validation_result['error'])
        
        file_extension = validation_result['file_info']['extension']
        
        try:
            if file_extension == '.pdf':
                return self.extract_text_from_pdf(file)
            elif file_extension == '.docx':
                return self.extract_text_from_docx(file)
            elif file_extension == '.doc':
                return self.extract_text_from_doc(file)
            elif file_extension == '.txt':
                return self.extract_text_from_txt(file)
            else:
                raise Exception(f"Unsupported file type: {file_extension}")
                
        except Exception as e:
            logger.error(f"Error extracting text from {file.name}: {e}")
            raise
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize extracted text"""
        if not text:
            return ""
        
        # Remove excessive whitespace
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if line:  # Skip empty lines
                cleaned_lines.append(line)
        
        # Join lines with single newlines
        cleaned_text = '\n'.join(cleaned_lines)
        
        # Remove excessive spaces
        import re
        cleaned_text = re.sub(r' +', ' ', cleaned_text)
        
        return cleaned_text.strip()


# Global instance
file_processor = FileProcessor()
