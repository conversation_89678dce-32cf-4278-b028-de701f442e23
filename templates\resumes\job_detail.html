{% extends 'base.html' %}
{% load resume_extras %}

{% block title %}{{ job.title }} - Resume Parser{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2>{{ job.title }}</h2>
                    <h5 class="text-muted">{{ job.company }}</h5>
                </div>
                <div>
                    <a href="{% url 'job_list' %}" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>Back to Jobs
                    </a>
                    <a href="{% url 'scoring_criteria_create' job.pk %}" class="btn btn-warning">
                        <i class="fas fa-chart-bar me-1"></i>Set Scoring Criteria
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Job Details -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Job Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Experience Level:</strong>
                            <span class="badge bg-primary ms-2">{{ job.get_experience_level_display }}</span>
                        </div>
                        <div class="col-md-6">
                            {% if job.location %}
                                <strong>Location:</strong> {{ job.location }}
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if job.salary_range %}
                        <div class="row mb-3">
                            <div class="col-12">
                                <strong>Salary Range:</strong> {{ job.salary_range }}
                            </div>
                        </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <strong>Description:</strong>
                        <div class="mt-2">{{ job.description|linebreaks }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Requirements:</strong>
                        <div class="mt-2">{{ job.requirements|linebreaks }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Skills Required:</strong>
                        <div class="mt-2">
                            {% for skill in job.skills_required|split:"," %}
                                <span class="badge bg-secondary me-1">{{ skill|trim }}</span>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h4 class="text-primary">{{ total_applications }}</h4>
                            <small class="text-muted">Total Applications</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-success">{{ shortlisted_count }}</h4>
                            <small class="text-muted">Shortlisted</small>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <a href="{% url 'batch_score_resumes' %}?job_id={{ job.pk }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-chart-bar me-1"></i>Score Resumes
                        </a>
                        <a href="{% url 'resume_scores_list' %}?job_id={{ job.pk }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-list me-1"></i>View All Scores
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Scored Resumes -->
    {% if resume_scores %}
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-trophy me-2"></i>Top Scored Resumes
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Candidate</th>
                                        <th>Overall Score</th>
                                        <th>Skills</th>
                                        <th>Experience</th>
                                        <th>Education</th>
                                        <th>Shortlisted</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for score in resume_scores %}
                                        <tr>
                                            <td>
                                                <strong>{{ score.resume.candidate_name|default:score.resume.original_filename }}</strong>
                                                {% if score.resume.candidate_email %}
                                                    <br><small class="text-muted">{{ score.resume.candidate_email }}</small>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="badge score-badge {% if score.overall_score >= 80 %}score-high{% elif score.overall_score >= 60 %}score-medium{% else %}score-low{% endif %}">
                                                    {{ score.overall_score|floatformat:1 }}%
                                                </span>
                                            </td>
                                            <td>{{ score.skills_score|floatformat:1 }}%</td>
                                            <td>{{ score.experience_score|floatformat:1 }}%</td>
                                            <td>{{ score.education_score|floatformat:1 }}%</td>
                                            <td>
                                                {% if score.is_shortlisted %}
                                                    <span class="badge bg-success">Yes</span>
                                                {% else %}
                                                    <span class="badge bg-danger">No</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{% url 'resume_score_detail' score.pk %}" class="btn btn-sm btn-outline-primary">
                                                    View Details
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        {% if total_applications > 10 %}
                            <div class="text-center mt-3">
                                <a href="{% url 'resume_scores_list' %}?job_id={{ job.pk }}" class="btn btn-primary">
                                    View All {{ total_applications }} Applications
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
