#!/usr/bin/env python
"""
Simple test script to verify the resume parser system is working
"""
import os
import sys
import django
from django.conf import settings

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'resume_parser_project.settings')
django.setup()

from django.contrib.auth.models import User
from resumes.models import JobDescription, Resume, ResumeAnalysis
from scoring.models import ScoringCriteria, ResumeScore
from services.ollama_service import ollama_service
from services.file_processor import file_processor


def test_ollama_connection():
    """Test Ollama connection and model availability"""
    print("🔍 Testing Ollama connection...")
    
    if not ollama_service.is_ollama_running():
        print("❌ Ollama server is not running")
        return False
    
    print("✅ Ollama server is running")
    
    models = ollama_service.list_models()
    if not models:
        print("❌ No models available")
        return False
    
    print(f"✅ Found {len(models)} model(s):")
    for model in models:
        print(f"   - {model.get('name', 'Unknown')}")
    
    return True


def test_llm_response():
    """Test LLM response generation"""
    print("\n🤖 Testing LLM response generation...")
    
    test_prompt = "Hello! Please respond with 'System is working correctly!'"
    response = ollama_service.generate_response(test_prompt)
    
    if response['success']:
        print(f"✅ LLM response: {response['response']}")
        print(f"   Processing time: {response['processing_time']:.2f} seconds")
        return True
    else:
        print(f"❌ LLM response failed: {response.get('error', 'Unknown error')}")
        return False


def test_database_models():
    """Test database models creation"""
    print("\n📊 Testing database models...")
    
    try:
        # Create test user
        user, created = User.objects.get_or_create(
            username='test_user',
            defaults={'email': '<EMAIL>'}
        )
        print(f"✅ User: {'Created' if created else 'Found'} test user")
        
        # Create test job description
        job, created = JobDescription.objects.get_or_create(
            title='Test Python Developer',
            company='Test Company',
            defaults={
                'description': 'Test job description for Python developer position',
                'requirements': 'Python, Django, PostgreSQL experience required',
                'skills_required': 'Python, Django, PostgreSQL, REST APIs',
                'experience_level': 'mid',
                'created_by': user
            }
        )
        print(f"✅ Job Description: {'Created' if created else 'Found'} test job")
        
        # Create scoring criteria
        criteria, created = ScoringCriteria.objects.get_or_create(
            job_description=job,
            defaults={
                'skills_weight': 40,
                'experience_weight': 30,
                'education_weight': 20,
                'other_weight': 10,
                'minimum_score': 60.0
            }
        )
        print(f"✅ Scoring Criteria: {'Created' if created else 'Found'} test criteria")
        
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False


def test_resume_analysis():
    """Test resume analysis with sample text"""
    print("\n📄 Testing resume analysis...")
    
    sample_resume_text = """
    John Doe
    Email: <EMAIL>
    Phone: (*************
    
    EXPERIENCE:
    Senior Python Developer at Tech Corp (2020-2023)
    - Developed web applications using Django and PostgreSQL
    - Built REST APIs and microservices
    - Led a team of 3 developers
    
    Python Developer at StartupXYZ (2018-2020)
    - Created data processing pipelines
    - Worked with Flask and SQLAlchemy
    
    EDUCATION:
    Bachelor of Science in Computer Science
    University of Technology (2014-2018)
    
    SKILLS:
    Python, Django, PostgreSQL, REST APIs, Flask, JavaScript, Git
    
    CERTIFICATIONS:
    AWS Certified Developer
    """
    
    try:
        response = ollama_service.analyze_resume(sample_resume_text)
        
        if response['success']:
            print("✅ Resume analysis successful")
            print(f"   Processing time: {response['processing_time']:.2f} seconds")
            print(f"   Response preview: {response['response'][:200]}...")
            return True
        else:
            print(f"❌ Resume analysis failed: {response.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Resume analysis test failed: {e}")
        return False


def test_scoring():
    """Test resume scoring"""
    print("\n🎯 Testing resume scoring...")
    
    # Sample resume analysis data
    resume_analysis = {
        'candidate_name': 'John Doe',
        'candidate_email': '<EMAIL>',
        'skills': ['Python', 'Django', 'PostgreSQL', 'REST APIs'],
        'experience_years': 5,
        'education': [{'degree': 'Bachelor of Science in Computer Science', 'institution': 'University of Technology'}],
        'work_experience': [
            {'company': 'Tech Corp', 'position': 'Senior Python Developer', 'duration': '2020-2023'},
            {'company': 'StartupXYZ', 'position': 'Python Developer', 'duration': '2018-2020'}
        ]
    }
    
    # Sample job description
    job_data = {
        'title': 'Python Developer',
        'company': 'Test Company',
        'description': 'Looking for experienced Python developer',
        'requirements': 'Python, Django, PostgreSQL experience required',
        'skills_required': 'Python, Django, PostgreSQL, REST APIs',
        'experience_level': 'mid'
    }
    
    try:
        response = ollama_service.score_resume(resume_analysis, job_data)
        
        if response['success']:
            print("✅ Resume scoring successful")
            print(f"   Processing time: {response['processing_time']:.2f} seconds")
            print(f"   Response preview: {response['response'][:200]}...")
            return True
        else:
            print(f"❌ Resume scoring failed: {response.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Resume scoring test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 Starting Resume Parser System Tests\n")
    
    tests = [
        test_ollama_connection,
        test_llm_response,
        test_database_models,
        test_resume_analysis,
        test_scoring
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your resume parser system is ready to use.")
        print("\n📝 Next steps:")
        print("1. Open http://127.0.0.1:8000/ in your browser")
        print("2. Create a job description")
        print("3. Upload some resume files")
        print("4. Set up scoring criteria")
        print("5. Run batch scoring to see the AI in action!")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        
    return passed == total


if __name__ == '__main__':
    main()
