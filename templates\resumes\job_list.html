{% extends 'base.html' %}

{% block title %}Job Descriptions - Resume Parser{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>
                    <i class="fas fa-briefcase me-2"></i>Job Descriptions
                </h2>
                <a href="{% url 'job_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>Create New Job
                </a>
            </div>
        </div>
    </div>

    <!-- Search Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            {{ form.search }}
                        </div>
                        <div class="col-md-3">
                            {{ form.experience_level }}
                        </div>
                        <div class="col-md-3">
                            {{ form.company }}
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-outline-primary w-100">
                                <i class="fas fa-search me-1"></i>Search
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Job List -->
    <div class="row">
        <div class="col-12">
            {% if page_obj %}
                <div class="row">
                    {% for job in page_obj %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <a href="{% url 'job_detail' job.pk %}" class="text-decoration-none">
                                            {{ job.title }}
                                        </a>
                                    </h5>
                                    <h6 class="card-subtitle mb-2 text-muted">{{ job.company }}</h6>
                                    
                                    <div class="mb-2">
                                        <span class="badge bg-primary">{{ job.get_experience_level_display }}</span>
                                        {% if job.location %}
                                            <span class="badge bg-secondary">{{ job.location }}</span>
                                        {% endif %}
                                    </div>
                                    
                                    <p class="card-text">
                                        {{ job.description|truncatewords:20 }}
                                    </p>
                                    
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <strong>Skills:</strong> {{ job.skills_required|truncatewords:8 }}
                                        </small>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            Created {{ job.created_at|date:"M d, Y" }}
                                        </small>
                                        <div>
                                            <a href="{% url 'job_detail' job.pk %}" class="btn btn-sm btn-outline-primary">
                                                View Details
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="Job pagination">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.experience_level %}&experience_level={{ request.GET.experience_level }}{% endif %}{% if request.GET.company %}&company={{ request.GET.company }}{% endif %}">First</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.experience_level %}&experience_level={{ request.GET.experience_level }}{% endif %}{% if request.GET.company %}&company={{ request.GET.company }}{% endif %}">Previous</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.experience_level %}&experience_level={{ request.GET.experience_level }}{% endif %}{% if request.GET.company %}&company={{ request.GET.company }}{% endif %}">Next</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.experience_level %}&experience_level={{ request.GET.experience_level }}{% endif %}{% if request.GET.company %}&company={{ request.GET.company }}{% endif %}">Last</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                    <h4>No Job Descriptions Found</h4>
                    <p class="text-muted">Create your first job description to get started with resume scoring.</p>
                    <a href="{% url 'job_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Create First Job
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
