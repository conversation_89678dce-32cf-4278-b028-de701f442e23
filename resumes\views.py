import json
import logging
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt

from .models import JobDescription, Resume, ResumeAnalysis
from .forms import JobDescriptionForm, ResumeUploadForm, BulkResumeUploadForm, JobDescriptionSearchForm, ResumeSearchForm
from services.file_processor import file_processor
from services.ollama_service import ollama_service

logger = logging.getLogger(__name__)


def home(request):
    """Home page view"""
    context = {
        'total_jobs': JobDescription.objects.filter(is_active=True).count(),
        'total_resumes': Resume.objects.count(),
        'processed_resumes': Resume.objects.filter(status='processed').count(),
        'recent_jobs': JobDescription.objects.filter(is_active=True).order_by('-created_at')[:5],
        'recent_resumes': Resume.objects.order_by('-uploaded_at')[:5],
    }
    return render(request, 'resumes/home.html', context)


def job_description_list(request):
    """List all job descriptions"""
    form = JobDescriptionSearchForm(request.GET)
    jobs = JobDescription.objects.filter(is_active=True).order_by('-created_at')

    if form.is_valid():
        search = form.cleaned_data.get('search')
        experience_level = form.cleaned_data.get('experience_level')
        company = form.cleaned_data.get('company')

        if search:
            jobs = jobs.filter(
                Q(title__icontains=search) |
                Q(company__icontains=search) |
                Q(skills_required__icontains=search) |
                Q(description__icontains=search)
            )

        if experience_level:
            jobs = jobs.filter(experience_level=experience_level)

        if company:
            jobs = jobs.filter(company__icontains=company)

    paginator = Paginator(jobs, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'form': form,
    }
    return render(request, 'resumes/job_list.html', context)


def job_description_create(request):
    """Create new job description"""
    if request.method == 'POST':
        form = JobDescriptionForm(request.POST)
        if form.is_valid():
            job = form.save(commit=False)
            if request.user.is_authenticated:
                job.created_by = request.user
            else:
                # Create or get a default user for testing
                from django.contrib.auth.models import User
                default_user, created = User.objects.get_or_create(
                    username='demo_user',
                    defaults={'email': '<EMAIL>'}
                )
                job.created_by = default_user
            job.save()
            messages.success(request, f'Job description "{job.title}" created successfully!')
            return redirect('job_detail', pk=job.pk)
    else:
        form = JobDescriptionForm()

    return render(request, 'resumes/job_form.html', {'form': form, 'title': 'Create Job Description'})


def job_description_detail(request, pk):
    """Job description detail view"""
    job = get_object_or_404(JobDescription, pk=pk)

    # Get related resume scores
    resume_scores = job.resume_scores.select_related('resume').order_by('-overall_score')[:10]

    context = {
        'job': job,
        'resume_scores': resume_scores,
        'total_applications': job.resume_scores.count(),
        'shortlisted_count': job.resume_scores.filter(is_shortlisted=True).count(),
    }
    return render(request, 'resumes/job_detail.html', context)


@login_required
def resume_list(request):
    """List all resumes"""
    form = ResumeSearchForm(request.GET)
    resumes = Resume.objects.select_related('uploaded_by').order_by('-uploaded_at')

    if form.is_valid():
        search = form.cleaned_data.get('search')
        status = form.cleaned_data.get('status')
        date_from = form.cleaned_data.get('date_from')
        date_to = form.cleaned_data.get('date_to')

        if search:
            resumes = resumes.filter(
                Q(candidate_name__icontains=search) |
                Q(candidate_email__icontains=search) |
                Q(original_filename__icontains=search) |
                Q(extracted_text__icontains=search)
            )

        if status:
            resumes = resumes.filter(status=status)

        if date_from:
            resumes = resumes.filter(uploaded_at__date__gte=date_from)

        if date_to:
            resumes = resumes.filter(uploaded_at__date__lte=date_to)

    paginator = Paginator(resumes, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'form': form,
    }
    return render(request, 'resumes/resume_list.html', context)


def resume_upload(request):
    """Single resume upload"""
    if request.method == 'POST':
        form = ResumeUploadForm(request.POST, request.FILES)
        if form.is_valid():
            resume = form.save(commit=False)
            if request.user.is_authenticated:
                resume.uploaded_by = request.user
            else:
                # Create or get a default user for testing
                from django.contrib.auth.models import User
                default_user, created = User.objects.get_or_create(
                    username='demo_user',
                    defaults={'email': '<EMAIL>'}
                )
                resume.uploaded_by = default_user
            resume.original_filename = resume.file.name
            resume.file_size = resume.file.size
            resume.save()

            # Process the resume asynchronously
            process_resume_async(resume.id)

            messages.success(request, f'Resume "{resume.original_filename}" uploaded successfully!')
            return redirect('resume_detail', pk=resume.pk)
    else:
        form = ResumeUploadForm()

    return render(request, 'resumes/resume_upload.html', {'form': form})


@login_required
def bulk_resume_upload(request):
    """Bulk resume upload"""
    if request.method == 'POST':
        form = BulkResumeUploadForm(request.POST, request.FILES)
        if form.is_valid():
            files = request.FILES.getlist('files')
            job_description = form.cleaned_data['job_description']
            auto_process = form.cleaned_data['auto_process']

            uploaded_resumes = []

            for file in files:
                try:
                    resume = Resume(
                        file=file,
                        original_filename=file.name,
                        file_size=file.size,
                        uploaded_by=request.user
                    )
                    resume.save()
                    uploaded_resumes.append(resume)

                    if auto_process:
                        process_resume_async(resume.id)

                except Exception as e:
                    logger.error(f"Error uploading {file.name}: {e}")
                    messages.error(request, f'Error uploading {file.name}: {str(e)}')

            if uploaded_resumes:
                messages.success(
                    request,
                    f'Successfully uploaded {len(uploaded_resumes)} resumes!'
                )

                if auto_process:
                    messages.info(
                        request,
                        'Resumes are being processed in the background. Check back in a few minutes.'
                    )

                return redirect('resume_list')
    else:
        form = BulkResumeUploadForm()

    return render(request, 'resumes/bulk_upload.html', {'form': form})


@login_required
def resume_detail(request, pk):
    """Resume detail view"""
    resume = get_object_or_404(Resume, pk=pk)

    # Get analysis if available
    analysis = None
    try:
        analysis = resume.analysis
    except ResumeAnalysis.DoesNotExist:
        pass

    # Get scores for this resume
    scores = resume.scores.select_related('job_description').order_by('-overall_score')

    context = {
        'resume': resume,
        'analysis': analysis,
        'scores': scores,
    }
    return render(request, 'resumes/resume_detail.html', context)


@login_required
@require_http_methods(["POST"])
def process_resume(request, pk):
    """Process a single resume"""
    resume = get_object_or_404(Resume, pk=pk)

    if resume.status == 'processing':
        return JsonResponse({'error': 'Resume is already being processed'}, status=400)

    try:
        process_resume_sync(resume)
        return JsonResponse({'success': True, 'message': 'Resume processed successfully'})
    except Exception as e:
        logger.error(f"Error processing resume {resume.id}: {e}")
        return JsonResponse({'error': str(e)}, status=500)


def process_resume_sync(resume):
    """Synchronously process a resume"""
    try:
        resume.status = 'processing'
        resume.save()

        # Extract text from file
        logger.info(f"Extracting text from {resume.original_filename}")
        extracted_text = file_processor.extract_text(resume.file)
        cleaned_text = file_processor.clean_text(extracted_text)

        resume.extracted_text = cleaned_text
        resume.save()

        # Analyze with LLM
        logger.info(f"Analyzing resume {resume.id} with LLM")
        analysis_response = ollama_service.analyze_resume(cleaned_text)

        if analysis_response['success']:
            # Parse LLM response
            try:
                analysis_data = json.loads(analysis_response['response'])

                # Update resume with extracted info
                resume.candidate_name = analysis_data.get('candidate_name', '')
                resume.candidate_email = analysis_data.get('candidate_email', '')
                resume.candidate_phone = analysis_data.get('candidate_phone', '')
                resume.status = 'processed'
                resume.processed_at = timezone.now()
                resume.save()

                # Create analysis record
                ResumeAnalysis.objects.create(
                    resume=resume,
                    skills=json.dumps(analysis_data.get('skills', [])),
                    experience_years=analysis_data.get('experience_years'),
                    education=json.dumps(analysis_data.get('education', [])),
                    work_experience=json.dumps(analysis_data.get('work_experience', [])),
                    certifications=json.dumps(analysis_data.get('certifications', [])),
                    analysis_model=analysis_response['model'],
                    processing_time=analysis_response['processing_time'],
                    raw_llm_response=analysis_response['response']
                )

                logger.info(f"Successfully processed resume {resume.id}")

            except json.JSONDecodeError as e:
                logger.error(f"Error parsing LLM response for resume {resume.id}: {e}")
                resume.status = 'error'
                resume.processing_error = f"Error parsing LLM response: {str(e)}"
                resume.save()
                raise
        else:
            resume.status = 'error'
            resume.processing_error = analysis_response.get('error', 'Unknown error')
            resume.save()
            raise Exception(analysis_response.get('error', 'LLM analysis failed'))

    except Exception as e:
        resume.status = 'error'
        resume.processing_error = str(e)
        resume.save()
        logger.error(f"Error processing resume {resume.id}: {e}")
        raise


def process_resume_async(resume_id):
    """Asynchronously process a resume (placeholder for background task)"""
    # In a production environment, you would use Celery or similar
    # For now, we'll process synchronously
    try:
        resume = Resume.objects.get(id=resume_id)
        process_resume_sync(resume)
    except Resume.DoesNotExist:
        logger.error(f"Resume {resume_id} not found")
    except Exception as e:
        logger.error(f"Error in async processing of resume {resume_id}: {e}")
