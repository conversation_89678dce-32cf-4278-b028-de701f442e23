"""
Ollama service for LLM interactions
"""
import json
import time
import logging
from typing import Dict, Any, Optional, List
from django.conf import settings
import ollama
import requests

logger = logging.getLogger(__name__)


class OllamaService:
    """Service class for interacting with Ollama LLM"""
    
    def __init__(self):
        self.base_url = settings.OLLAMA_BASE_URL
        self.model = settings.OLLAMA_MODEL
        self.client = ollama.Client(host=self.base_url)
    
    def is_ollama_running(self) -> bool:
        """Check if Ollama server is running"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except requests.exceptions.RequestException:
            return False
    
    def list_models(self) -> List[Dict[str, Any]]:
        """List available models"""
        try:
            response = self.client.list()
            return response.get('models', [])
        except Exception as e:
            logger.error(f"Error listing models: {e}")
            return []
    
    def pull_model(self, model_name: str) -> bool:
        """Pull a model from Ollama registry"""
        try:
            logger.info(f"Pulling model: {model_name}")
            self.client.pull(model_name)
            logger.info(f"Successfully pulled model: {model_name}")
            return True
        except Exception as e:
            logger.error(f"Error pulling model {model_name}: {e}")
            return False
    
    def generate_response(self, prompt: str, model: Optional[str] = None, 
                         system_prompt: Optional[str] = None) -> Dict[str, Any]:
        """Generate response from LLM"""
        start_time = time.time()
        model_name = model or self.model
        
        try:
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            response = self.client.chat(
                model=model_name,
                messages=messages,
                options={
                    'temperature': 0.1,  # Low temperature for consistent results
                    'top_p': 0.9,
                    'num_predict': 2048,  # Max tokens to generate
                }
            )
            
            processing_time = time.time() - start_time
            
            return {
                'success': True,
                'response': response['message']['content'],
                'model': model_name,
                'processing_time': processing_time,
                'raw_response': response
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Error generating response: {e}")
            return {
                'success': False,
                'error': str(e),
                'model': model_name,
                'processing_time': processing_time,
                'raw_response': None
            }
    
    def analyze_resume(self, resume_text: str) -> Dict[str, Any]:
        """Analyze resume and extract structured information"""
        system_prompt = """You are an expert resume analyzer. Extract structured information from the resume text and return it in JSON format.

Extract the following information:
1. candidate_name: Full name of the candidate
2. candidate_email: Email address
3. candidate_phone: Phone number
4. skills: Array of technical and soft skills
5. experience_years: Total years of experience (as integer)
6. education: Array of education details with degree, institution, year
7. work_experience: Array of work experience with company, position, duration, description
8. certifications: Array of certifications

Return only valid JSON without any additional text or formatting."""
        
        prompt = f"""Analyze this resume and extract structured information:

{resume_text}

Return the information in the exact JSON format specified in the system prompt."""
        
        return self.generate_response(prompt, system_prompt=system_prompt)
    
    def score_resume(self, resume_analysis: Dict[str, Any], job_description: Dict[str, Any]) -> Dict[str, Any]:
        """Score resume against job description"""
        system_prompt = """You are an expert HR professional. Score the resume against the job description and provide detailed analysis.

Provide scores (0-100) for:
1. skills_score: How well candidate's skills match job requirements
2. experience_score: How well candidate's experience matches job level and requirements
3. education_score: How well candidate's education matches job requirements
4. other_score: Other factors like certifications, achievements, etc.

Also provide detailed analysis for each category and overall recommendation.

Return only valid JSON without any additional text or formatting."""
        
        prompt = f"""Score this resume against the job description:

JOB DESCRIPTION:
Title: {job_description.get('title', '')}
Company: {job_description.get('company', '')}
Requirements: {job_description.get('requirements', '')}
Skills Required: {job_description.get('skills_required', '')}
Experience Level: {job_description.get('experience_level', '')}

RESUME ANALYSIS:
{json.dumps(resume_analysis, indent=2)}

Provide scoring in this JSON format:
{{
    "skills_score": <0-100>,
    "experience_score": <0-100>,
    "education_score": <0-100>,
    "other_score": <0-100>,
    "skills_analysis": "<detailed analysis of skills matching>",
    "experience_analysis": "<detailed analysis of experience>",
    "education_analysis": "<detailed analysis of education>",
    "overall_analysis": "<overall analysis and recommendation>",
    "shortlist_recommendation": "<recommend to shortlist or not and why>"
}}"""
        
        return self.generate_response(prompt, system_prompt=system_prompt)


# Global instance
ollama_service = OllamaService()
