{% extends 'base.html' %}

{% block title %}{{ title }} - Resume Parser{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>{{ title }}
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Job:</strong> {{ job.title }} at {{ job.company }}
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-12 mb-4">
                                <h5>Scoring Weights</h5>
                                <p class="text-muted">Define how much each category contributes to the overall score. All weights must sum to 100.</p>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="skills_weight" class="form-label">Skills Weight (%)</label>
                                <input type="number" class="form-control" id="skills_weight" name="skills_weight" 
                                       value="{% if criteria %}{{ criteria.skills_weight }}{% else %}40{% endif %}" 
                                       min="0" max="100" required>
                                <div class="form-text">How well candidate's skills match job requirements</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="experience_weight" class="form-label">Experience Weight (%)</label>
                                <input type="number" class="form-control" id="experience_weight" name="experience_weight" 
                                       value="{% if criteria %}{{ criteria.experience_weight }}{% else %}30{% endif %}" 
                                       min="0" max="100" required>
                                <div class="form-text">How well candidate's experience matches job level</div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="education_weight" class="form-label">Education Weight (%)</label>
                                <input type="number" class="form-control" id="education_weight" name="education_weight" 
                                       value="{% if criteria %}{{ criteria.education_weight }}{% else %}20{% endif %}" 
                                       min="0" max="100" required>
                                <div class="form-text">How well candidate's education matches requirements</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="other_weight" class="form-label">Other Factors Weight (%)</label>
                                <input type="number" class="form-control" id="other_weight" name="other_weight" 
                                       value="{% if criteria %}{{ criteria.other_weight }}{% else %}10{% endif %}" 
                                       min="0" max="100" required>
                                <div class="form-text">Certifications, achievements, etc.</div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="alert alert-warning" id="weight-warning" style="display: none;">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Warning:</strong> Weights must sum to 100. Current total: <span id="total-weight">100</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="minimum_score" class="form-label">Minimum Score for Shortlisting</label>
                                <input type="number" class="form-control" id="minimum_score" name="minimum_score" 
                                       value="{% if criteria %}{{ criteria.minimum_score }}{% else %}60.0{% endif %}" 
                                       min="0" max="100" step="0.1" required>
                                <div class="form-text">Candidates with scores below this will not be shortlisted</div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'job_detail' job.pk %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Back to Job
                            </a>
                            <button type="submit" class="btn btn-primary" id="submit-btn">
                                <i class="fas fa-save me-1"></i>Save Criteria
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const weightInputs = ['skills_weight', 'experience_weight', 'education_weight', 'other_weight'];
    const warningDiv = document.getElementById('weight-warning');
    const totalSpan = document.getElementById('total-weight');
    const submitBtn = document.getElementById('submit-btn');
    
    function updateWeightTotal() {
        let total = 0;
        weightInputs.forEach(function(inputId) {
            const input = document.getElementById(inputId);
            if (input) {
                total += parseInt(input.value) || 0;
            }
        });
        
        totalSpan.textContent = total;
        
        if (total !== 100) {
            warningDiv.style.display = 'block';
            submitBtn.disabled = true;
            submitBtn.classList.add('btn-secondary');
            submitBtn.classList.remove('btn-primary');
        } else {
            warningDiv.style.display = 'none';
            submitBtn.disabled = false;
            submitBtn.classList.add('btn-primary');
            submitBtn.classList.remove('btn-secondary');
        }
    }
    
    // Add event listeners to all weight inputs
    weightInputs.forEach(function(inputId) {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('input', updateWeightTotal);
        }
    });
    
    // Initial check
    updateWeightTotal();
});
</script>
{% endblock %}
{% endblock %}
