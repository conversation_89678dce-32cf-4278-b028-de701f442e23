# Django Bulk Resume Parser and Scoring System with Ollama LLM

A comprehensive Django application that uses Ollama LLM to parse resumes, extract candidate information, and score them against job descriptions for efficient candidate shortlisting.

## 🚀 Features

- **Bulk Resume Upload**: Upload multiple resumes in PDF, DOC, DOCX, or TXT format
- **AI-Powered Analysis**: Uses Ollama LLM (llama3.2:3b) to extract candidate information
- **Intelligent Scoring**: Scores resumes against job descriptions with customizable weights
- **Automated Shortlisting**: Automatically shortlists candidates based on minimum score thresholds
- **Web Interface**: Clean, responsive Bootstrap-based interface
- **Batch Processing**: Process multiple resumes simultaneously
- **Detailed Analytics**: View detailed scoring breakdowns and candidate analysis

## 🛠️ System Requirements

- **CPU**: Intel i5 13450hx (or equivalent)
- **RAM**: 24GB (recommended for optimal LLM performance)
- **Storage**: 512GB NVMe SSD
- **GPU**: RTX 4050 Mobile (optional, for faster LLM inference)
- **OS**: Windows 10/11, macOS, or Linux

## 📋 Prerequisites

1. **Python 3.12+** installed
2. **Ollama** installed and running
3. **Git** (optional, for version control)

## 🔧 Installation

### 1. Install Ollama

Download and install Ollama from [https://ollama.com/download](https://ollama.com/download)

### 2. Clone/Download the Project

```bash
# If using Git
git clone <repository-url>
cd resume-parser-test

# Or download and extract the ZIP file
```

### 3. Set Up Virtual Environment

```bash
python -m venv resume_parser_env
resume_parser_env\Scripts\activate  # Windows
# source resume_parser_env/bin/activate  # macOS/Linux
```

### 4. Install Dependencies

```bash
pip install -r requirements.txt
```

### 5. Set Up Ollama Model

```bash
python manage.py setup_ollama --model llama3.2:3b
```

### 6. Set Up Database

```bash
python manage.py migrate
python manage.py createsuperuser
```

## 🚀 Running the Application

### 1. Start Ollama (if not running)

```bash
ollama serve
```

### 2. Start Django Development Server

```bash
python manage.py runserver
```

### 3. Access the Application

Open your browser and navigate to: `http://127.0.0.1:8000/`

## 📖 Usage Guide

### 1. Create Job Descriptions

1. Navigate to **Jobs** → **Create New Job**
2. Fill in job details including:
   - Job title and company
   - Job description and requirements
   - Required skills (comma-separated)
   - Experience level and location

### 2. Set Up Scoring Criteria

1. Go to the job detail page
2. Click **Set Scoring Criteria**
3. Configure weights for:
   - Skills matching (default: 40%)
   - Experience level (default: 30%)
   - Education background (default: 20%)
   - Other factors (default: 10%)
4. Set minimum score for shortlisting (default: 60%)

### 3. Upload Resumes

**Single Upload:**
1. Navigate to **Resumes** → **Upload Resume**
2. Select a resume file (PDF, DOC, DOCX, TXT)
3. The system will automatically process and analyze the resume

**Bulk Upload:**
1. Navigate to **Resumes** → **Bulk Upload**
2. Select multiple resume files
3. Choose a job description for automatic scoring
4. Enable auto-processing for immediate analysis

### 4. Score Resumes

**Manual Scoring:**
1. Go to **Scoring** → **Batch Score**
2. Select a job description
3. Choose resumes to score
4. Start batch processing

**Automatic Scoring:**
- Enable during bulk upload for immediate scoring

### 5. Review Results

1. Navigate to **Scoring** → **All Scores**
2. Filter by job description or shortlisted status
3. View detailed scoring breakdowns
4. Export shortlisted candidates

## 🧪 Testing

Run the comprehensive test suite:

```bash
python test_resume_parser.py
```

This will test:
- Ollama connection and model availability
- LLM response generation
- Database models
- Resume analysis functionality
- Scoring system

## 📁 Project Structure

```
resume-parser-test/
├── manage.py
├── requirements.txt
├── test_resume_parser.py
├── README.md
├── .env
├── resume_parser_project/
│   ├── settings.py
│   ├── urls.py
│   └── ...
├── resumes/
│   ├── models.py
│   ├── views.py
│   ├── forms.py
│   ├── admin.py
│   └── ...
├── scoring/
│   ├── models.py
│   ├── views.py
│   ├── admin.py
│   └── ...
├── services/
│   ├── ollama_service.py
│   ├── file_processor.py
│   └── ...
└── templates/
    ├── base.html
    ├── resumes/
    └── scoring/
```

## ⚙️ Configuration

### Environment Variables (.env)

```env
DEBUG=True
SECRET_KEY=your-secret-key
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.2:3b
```

### Supported File Formats

- **PDF**: Portable Document Format
- **DOC**: Microsoft Word (legacy)
- **DOCX**: Microsoft Word (modern)
- **TXT**: Plain text files

### File Size Limits

- Maximum file size: 5MB per resume
- Bulk upload: Up to 50 files simultaneously

## 🔍 Troubleshooting

### Common Issues

1. **Ollama not running**
   ```bash
   ollama serve
   ```

2. **Model not found**
   ```bash
   python manage.py setup_ollama --model llama3.2:3b
   ```

3. **File upload errors**
   - Check file format (PDF, DOC, DOCX, TXT)
   - Ensure file size is under 5MB

4. **Slow processing**
   - Ensure sufficient RAM (24GB recommended)
   - Consider using GPU acceleration if available

### Performance Optimization

- **For better performance**: Use GPU-enabled Ollama setup
- **For memory constraints**: Use smaller models like `llama3.2:1b`
- **For production**: Consider using Celery for background processing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- **Ollama**: For providing excellent local LLM capabilities
- **Django**: For the robust web framework
- **Bootstrap**: For the responsive UI components
- **Meta**: For the Llama model architecture
